2025-07-17 11:17:41.684 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-17 11:17:41.697 [main] INFO  com.kumhosunny.app.KumhosunnyAiAppApplication - Starting KumhosunnyAiAppApplication using Java 17.0.12 on zhibiaodeMacBook-Pro.local with PID 80381 (/Users/<USER>/cursor_project/kumhosunny-ai-app/app/target/classes started by zhibiao in /Users/<USER>/cursor_project/kumhosunny-ai-app)
2025-07-17 11:17:41.698 [main] DEBUG com.kumhosunny.app.KumhosunnyAiAppApplication - Running with Spring Boot v2.7.8, Spring v5.3.25
2025-07-17 11:17:41.698 [main] INFO  com.kumhosunny.app.KumhosunnyAiAppApplication - The following 1 profile is active: "dev"
2025-07-17 11:17:41.999 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-17 11:17:42.087 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 85 ms. Found 17 JPA repository interfaces.
2025-07-17 11:17:42.548 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 3333 (http)
2025-07-17 11:17:42.553 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-3333"]
2025-07-17 11:17:42.553 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-17 11:17:42.553 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-17 11:17:42.620 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-17 11:17:42.620 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 896 ms
2025-07-17 11:17:42.691 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-17 11:17:42.714 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.14.Final
2025-07-17 11:17:42.777 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-17 11:17:42.826 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-17 11:17:43.019 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-17 11:17:43.027 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-17 11:17:43.350 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-17 11:17:43.353 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-17 11:17:44.278 [main] INFO  com.amazonaws.http.AmazonHttpClient - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7897
2025-07-17 11:17:44.888 [main] INFO  com.kumhosunny.common.config.QdrantConfig - 成功连接到Qdrant向量数据库: **************:6334
