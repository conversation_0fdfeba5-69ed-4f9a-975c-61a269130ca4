# 异步文件上传功能使用指南

## 概述

文件上传已改为异步处理，文件列表接口直接包含处理状态信息，无需单独查询状态。

## ✅ 完成的修改

1. **创建文件处理状态枚举** - `FileProcessStatus`
2. **修改AiFiles实体** - 支持状态枚举和错误信息
3. **创建异步处理服务** - `AsyncFileProcessService`
4. **重构upload接口** - 改为异步处理
5. **增强文件列表接口** - 直接包含完整状态信息
6. **移除独立状态查询接口** - 简化API设计
7. **优化查询逻辑** - 根据type参数精确查询指定类型文档
8. **修复事务问题** - 确保异步处理在主事务提交后执行
9. **修复向量化问题** - 将纯文本转换为JSON格式进行向量化处理
10. **优化文本分割策略** - 使用智能分割，保持语义完整性和上下文连贯性

## 主要接口

### 1. 文件上传接口

```http
POST /api/ai/file/upload
Content-Type: multipart/form-data

files: [文件]
type: personal|department|company|legal
```

**响应示例**：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 123,
      "fileName": "uuid-filename.pdf",
      "originalFileName": "document.pdf",
      "aiStatus": "UPLOADED",
      "uploadTime": "2025-01-17 10:30:00"
    }
  ]
}
```

### 2. 文件列表接口（包含状态信息）

```http
GET /api/ai/file/list?page=0&size=10
# 可选参数：
# type: personal|department|company|legal (指定查询哪种类型的文档)
# originalFileName: 文件名关键词搜索 (可选)
```

**type参数说明**：
- `personal`：只查询用户自己的个人文档
- `department`：只查询用户部门的文档
- `company`：只查询公司文档
- `legal`：只查询法务文档
- 不传type：查询所有有权限的文档

**注意**：
- 自动排除图片附件（jpg, jpeg, png, gif, bmp, webp, svg）
- 严格的权限控制，只显示用户有权限访问的文件
- 按文件ID倒序排列（最新上传的在前）

**查询规则**：
- `type=personal`：查询用户自己拥有的个人文档（ownerId为当前用户）
- `type=department`：查询用户部门的文档（departmentId在用户部门范围内）
- `type=company`：查询所有公司文档
- `type=legal`：查询所有法务文档
- 不指定type：查询所有有权限的文档
- 未关联文档的文件：只显示用户自己上传的

**响应示例**：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "content": [
      {
        "fileId": 123,
        "userId": 1,
        "originalFileName": "document.pdf",
        "fileUrl": "/bucket/uuid-filename.pdf",
        "path": "https://oss.example.com/bucket/uuid-filename.pdf",
        "fileType": "pdf",
        "fileSize": "1024000",
        "status": "VECTORIZING",
        "statusDescription": "向量化中",
        "errorMessage": null,
        "uploadTime": "2025-01-17 10:30:00",
        "documentId": 456,
        "documentTitle": "测试文档",
        "documentType": "personal",
        "documentCreatedAt": "2025-01-17T10:30:00",
        "isProcessing": true,
        "isCompleted": false,
        "isFailed": false
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 0
  }
}
```

## 状态说明

| 状态 | 描述 | isProcessing | isCompleted | isFailed |
|------|------|--------------|-------------|----------|
| UPLOADING | 上传中 | true | false | false |
| UPLOADED | 已上传 | false | false | false |
| PARSING | 解析中 | true | false | false |
| PARSED | 已解析 | false | false | false |
| VECTORIZING | 向量化中 | true | false | false |
| COMPLETED | 处理完成 | false | true | false |
| FAILED | 处理失败 | false | false | true |

## 前端集成示例

### 1. 上传文件

```javascript
async function uploadFile(file, type) {
  const formData = new FormData();
  formData.append('files', file);
  formData.append('type', type);
  
  const response = await fetch('/api/ai/file/upload', {
    method: 'POST',
    body: formData
  });
  
  return await response.json();
}
```

### 2. 获取文件列表并监控状态

```javascript
async function getFileList(page = 0, size = 10, type = null, fileName = null) {
  let url = `/api/ai/file/list?page=${page}&size=${size}`;
  if (type) url += `&type=${type}`;  // personal|department|company|legal
  if (fileName) url += `&originalFileName=${fileName}`;

  const response = await fetch(url);
  const result = await response.json();

  if (result.code === 200) {
    return result.data.content;
  }
  throw new Error(result.message);
}

// 示例：查询不同类型的文档
const personalFiles = await getFileList(0, 10, 'personal');    // 个人文档
const departmentFiles = await getFileList(0, 10, 'department'); // 部门文档
const companyFiles = await getFileList(0, 10, 'company');      // 公司文档
const allFiles = await getFileList(0, 10);                     // 所有文档
```

// 监控处理中的文件
function monitorProcessingFiles() {
  setInterval(async () => {
    try {
      const files = await getFileList();
      const processingFiles = files.filter(file => file.isProcessing);
      
      if (processingFiles.length > 0) {
        console.log(`有 ${processingFiles.length} 个文件正在处理中`);
        // 更新UI显示处理状态
        updateFileListUI(files);
      }
    } catch (error) {
      console.error('获取文件列表失败:', error);
    }
  }, 3000); // 每3秒检查一次
}
```

### 3. 文件状态显示组件

```javascript
function FileStatusBadge({ file }) {
  if (file.isProcessing) {
    return <span className="badge processing">{file.statusDescription}</span>;
  } else if (file.isCompleted) {
    return <span className="badge success">处理完成</span>;
  } else if (file.isFailed) {
    return <span className="badge error" title={file.errorMessage}>处理失败</span>;
  } else {
    return <span className="badge pending">{file.statusDescription}</span>;
  }
}
```

## 优势

1. **简化接口调用**：文件列表直接包含状态，无需额外查询
2. **实时状态更新**：定期刷新列表即可获取最新状态
3. **完整状态信息**：包含状态描述、错误信息、处理标志等
4. **向后兼容**：保持原有接口结构，只是增加了状态字段
5. **智能过滤**：自动排除图片附件，专注文档处理
6. **严格权限控制**：确保用户只能看到有权限访问的文件

## 配置说明

### 1. 文档解析服务地址配置

在 `application.yml` 中配置正确的文档解析服务地址：

```yaml
app:
  document:
    parse-url: http://your-document-parse-service:port/parse
```

或在 `application.properties` 中：
```properties
app.document.parse-url=http://your-document-parse-service:port/parse
```

### 2. 文本分割策略配置

当前使用智能分割策略，参数可调：
- **maxChunkSize**: 800字符（最大chunk大小）
- **overlapSize**: 100字符（chunk间重叠大小，保持上下文连贯性）

**分割策略特点**：
- 优先按段落分割，保持语义完整性
- 段落过长时按句子分割
- 句子过长时强制按字符分割
- 支持chunk间重叠，提高检索效果
- 智能处理中文标点符号

## 注意事项

1. 建议每3-5秒刷新一次文件列表来获取最新状态
2. 可以根据`isProcessing`字段判断是否需要继续监控
3. 失败的文件会在`errorMessage`字段显示具体错误信息
4. 刚上传的文件可能暂时没有`documentId`，这是正常的
5. 确保文档解析服务地址配置正确
6. 文本分割策略已优化，支持中文文档的语义分割
