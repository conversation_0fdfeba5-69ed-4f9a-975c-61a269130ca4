package com.kumhosunny.knowledge.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.amazonaws.services.s3.model.S3Object;
import com.kumhosunny.common.entity.AiFiles;
import com.kumhosunny.common.entity.Document;
import com.kumhosunny.common.enums.FileProcessStatus;
import com.kumhosunny.common.enums.VectorizableFileType;
import com.kumhosunny.common.repository.AiFilesRepository;
import com.kumhosunny.common.util.HashUtils;
import com.kumhosunny.common.util.LiteratureQdrantUtils;
import com.kumhosunny.knowledge.service.AsyncFileProcessService;
import com.pig4cloud.plugin.oss.service.OssTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 异步文件处理服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class AsyncFileProcessServiceImpl implements AsyncFileProcessService {

    @Autowired
    private AiFilesRepository aiFilesRepository;

    @Autowired
    private LiteratureQdrantUtils literatureQdrantService;

    @Autowired
    private OssTemplate ossTemplate;

    @Value("${app.document.parse-url:http://192.168.170.66:8100/general/v0/general}")
    private String documentParseUrl;

    @Value("${app.qdrant.collection-name:literature_collection}")
    private String collectionName;

    @Override
    @Async("taskExecutor")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void processFileAsync(Long fileId, String type) {
        log.info("开始异步处理文件: fileId={}, type={}", fileId, type);

        try {
            Optional<AiFiles> fileOptional = aiFilesRepository.findById(fileId);
            if (!fileOptional.isPresent()) {
                log.error("文件不存在: fileId={}", fileId);
                return;
            }

            AiFiles aiFiles = fileOptional.get();

            // 检查文件类型是否支持向量化
            if (ObjectUtil.isEmpty(VectorizableFileType.fromExtension(aiFiles.getFileType()))) {
                log.info("文件类型不支持向量化: fileType={}", aiFiles.getFileType());
                updateFileStatus(fileId, FileProcessStatus.COMPLETED);
                return;
            }

            // 检查是否需要解析文档
            if (StrUtil.isBlank(type)) {
                log.info("未指定文档类型，跳过解析: fileId={}", fileId);
                updateFileStatus(fileId, FileProcessStatus.COMPLETED);
                return;
            }

            // 步骤1: 解析文件
            parseFile(aiFiles, type);

        } catch (Exception e) {
            log.error("异步处理文件失败: fileId={}", fileId, e);
            updateFileStatus(fileId, FileProcessStatus.FAILED, "处理失败: " + e.getMessage());
        }
    }

    /**
     * 解析文件内容
     */
    private void parseFile(AiFiles aiFiles, String type) {
        log.info("开始解析文件: fileId={}, fileName={}", aiFiles.getId(), aiFiles.getFileName());

        try {
            // 更新状态为解析中
            updateFileStatus(aiFiles.getId(), FileProcessStatus.PARSING);

            // 创建临时文件用于解析
            File tempFile = createTempFile(aiFiles);
            if (tempFile == null) {
                throw new RuntimeException("无法创建临时文件");
            }

            try {
                // 调用文档解析服务
                HttpRequest httpRequest = HttpUtil.createPost(documentParseUrl);
                httpRequest.charset("UTF-8");
                httpRequest.form("encoding", "UTF-8");
                httpRequest.form("files", tempFile);
                String parsedContent = httpRequest.execute().body();

                if (StrUtil.isBlank(parsedContent)) {
                    throw new RuntimeException("文档解析结果为空");
                }

                // 保存解析内容
                aiFiles.setTextContent(parsedContent);
                aiFilesRepository.save(aiFiles);

                // 更新状态为已解析
                updateFileStatus(aiFiles.getId(), FileProcessStatus.PARSED);

                log.info("文件解析完成: fileId={}, contentLength={}", aiFiles.getId(), parsedContent.length());

                // 步骤2: 向量化处理
                vectorizeFile(aiFiles, type, parsedContent);

            } finally {
                // 清理临时文件
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }

        } catch (Exception e) {
            log.error("文件解析失败: fileId={}", aiFiles.getId(), e);
            updateFileStatus(aiFiles.getId(), FileProcessStatus.FAILED, "解析失败: " + e.getMessage());
        }
    }

    /**
     * 向量化处理
     */
    private void vectorizeFile(AiFiles aiFiles, String type, String content) {
        log.info("开始向量化文件: fileId={}, type={}", aiFiles.getId(), type);

        try {
            // 更新状态为向量化中
            updateFileStatus(aiFiles.getId(), FileProcessStatus.VECTORIZING);

            // 内容去重检查
            if (shouldCheckDuplicates(type)) {
                String contentHash = HashUtils.normalizedContentHash(content);
                // TODO: 实现重复内容检查逻辑
                log.debug("内容哈希: {}", contentHash);
            }

            // 将纯文本内容转换为JSON格式的chunks
            String chunkedContent = convertTextToChunkedJson(content);
            log.debug("文本分块完成: fileId={}, 原始长度={}, 分块后长度={}",
                    aiFiles.getId(), content.length(), chunkedContent.length());

            // 执行向量化
            Document.DocumentType documentType = Document.DocumentType.valueOf(type);
            int insertedChunks = literatureQdrantService.insertLiterature(chunkedContent, collectionName, aiFiles,
                    documentType);

            log.info("文档向量化完成: fileId={}, 插入段落数: {}", aiFiles.getId(), insertedChunks);

            // 清理文本内容（节省存储空间）
            aiFiles.setTextContent("");
            aiFilesRepository.save(aiFiles);

            // 更新状态为完成
            updateFileStatus(aiFiles.getId(), FileProcessStatus.COMPLETED);

        } catch (Exception e) {
            log.error("文件向量化失败: fileId={}", aiFiles.getId(), e);
            updateFileStatus(aiFiles.getId(), FileProcessStatus.FAILED, "向量化失败: " + e.getMessage());
        }
    }

    /**
     * 将纯文本转换为JSON格式的chunks
     */
    private String convertTextToChunkedJson(String content) {
        if (StrUtil.isBlank(content)) {
            return "[]";
        }

        // 使用智能分割策略
        List<String> textChunks = smartSplitText(content, 800, 100);

        // 转换为JSON数组格式
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < textChunks.size(); i++) {
            JSONObject chunkObj = new JSONObject();
            chunkObj.set("text", textChunks.get(i));
            chunkObj.set("chunk_index", i);
            jsonArray.add(chunkObj);
        }

        return jsonArray.toString();
    }

    /**
     * 智能文本分割策略
     * 优先按句子分割，保持语义完整性
     *
     * @param content      原始文本
     * @param maxChunkSize 最大chunk大小
     * @param overlapSize  重叠大小（用于保持上下文连贯性）
     * @return 分割后的文本块列表
     */
    private List<String> smartSplitText(String content, int maxChunkSize, int overlapSize) {
        List<String> chunks = new ArrayList<>();
        if (StrUtil.isBlank(content)) {
            return chunks;
        }

        // 1. 首先按段落分割
        String[] paragraphs = content.split("\\n\\s*\\n");

        StringBuilder currentChunk = new StringBuilder();
        String previousChunkEnd = ""; // 用于重叠

        for (String paragraph : paragraphs) {
            paragraph = paragraph.trim();
            if (paragraph.isEmpty())
                continue;

            // 2. 如果单个段落就超过最大大小，需要进一步分割
            if (paragraph.length() > maxChunkSize) {
                // 先保存当前chunk（如果有内容）
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString().trim());
                    previousChunkEnd = getChunkEnd(currentChunk.toString(), overlapSize);
                    currentChunk = new StringBuilder();
                }

                // 分割长段落
                List<String> subChunks = splitLongParagraph(paragraph, maxChunkSize, overlapSize);
                for (int i = 0; i < subChunks.size(); i++) {
                    String subChunk = subChunks.get(i);
                    if (i == 0 && !previousChunkEnd.isEmpty()) {
                        // 第一个子chunk添加重叠内容
                        chunks.add(previousChunkEnd + " " + subChunk);
                    } else {
                        chunks.add(subChunk);
                    }
                    if (i < subChunks.size() - 1) {
                        previousChunkEnd = getChunkEnd(subChunk, overlapSize);
                    }
                }
                continue;
            }

            // 3. 检查添加当前段落是否会超过大小限制
            if (currentChunk.length() + paragraph.length() + 2 > maxChunkSize && currentChunk.length() > 0) {
                // 保存当前chunk
                chunks.add(currentChunk.toString().trim());
                previousChunkEnd = getChunkEnd(currentChunk.toString(), overlapSize);

                // 开始新chunk，添加重叠内容
                currentChunk = new StringBuilder();
                if (!previousChunkEnd.isEmpty()) {
                    currentChunk.append(previousChunkEnd).append(" ");
                }
            }

            // 4. 添加当前段落
            if (currentChunk.length() > 0) {
                currentChunk.append("\\n\\n");
            }
            currentChunk.append(paragraph);
        }

        // 5. 保存最后一个chunk
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString().trim());
        }

        // 如果没有生成任何chunk，至少返回原始内容
        if (chunks.isEmpty() && !content.trim().isEmpty()) {
            chunks.add(content.trim());
        }

        return chunks;
    }

    /**
     * 分割长段落
     */
    private List<String> splitLongParagraph(String paragraph, int maxChunkSize, int overlapSize) {
        List<String> chunks = new ArrayList<>();

        // 按句子分割
        String[] sentences = paragraph.split("[。！？；;]");

        StringBuilder currentChunk = new StringBuilder();

        for (String sentence : sentences) {
            sentence = sentence.trim();
            if (sentence.isEmpty())
                continue;

            // 如果单个句子就超过最大大小，强制分割
            if (sentence.length() > maxChunkSize) {
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString().trim());
                    currentChunk = new StringBuilder();
                }

                // 强制按字符分割
                for (int i = 0; i < sentence.length(); i += maxChunkSize - overlapSize) {
                    int end = Math.min(i + maxChunkSize, sentence.length());
                    chunks.add(sentence.substring(i, end));
                }
                continue;
            }

            // 检查添加当前句子是否会超过大小限制
            if (currentChunk.length() + sentence.length() + 1 > maxChunkSize && currentChunk.length() > 0) {
                chunks.add(currentChunk.toString().trim());
                currentChunk = new StringBuilder();
            }

            if (currentChunk.length() > 0) {
                currentChunk.append("。");
            }
            currentChunk.append(sentence);
        }

        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString().trim());
        }

        return chunks;
    }

    /**
     * 获取chunk末尾的重叠内容
     */
    private String getChunkEnd(String chunk, int overlapSize) {
        if (chunk.length() <= overlapSize) {
            return chunk;
        }

        String end = chunk.substring(chunk.length() - overlapSize);

        // 尝试从完整的句子开始
        int lastSentenceStart = end.lastIndexOf("。");
        if (lastSentenceStart > 0 && lastSentenceStart < end.length() - 1) {
            return end.substring(lastSentenceStart + 1).trim();
        }

        return end.trim();
    }

    /**
     * 创建临时文件
     */
    private File createTempFile(AiFiles aiFiles) {
        try {
            String tempFileName = aiFiles.getFileName();
            File tempFile = new File(System.getProperty("java.io.tmpdir") + "/" + tempFileName);

            // 从OSS下载文件到临时目录
            try (S3Object s3Object = ossTemplate.getObject(aiFiles.getBucketName(), aiFiles.getFileName())) {
                FileUtil.writeFromStream(s3Object.getObjectContent(), tempFile);
                log.debug("文件下载成功: {}", tempFile.getAbsolutePath());
                return tempFile;
            }

        } catch (Exception e) {
            log.error("创建临时文件失败: fileName={}", aiFiles.getFileName(), e);
            return null;
        }
    }

    /**
     * 判断是否需要检查重复内容
     */
    private boolean shouldCheckDuplicates(String type) {
        return "department".equals(type) || "company".equals(type);
    }

    @Override
    @Transactional
    public void updateFileStatus(Long fileId, FileProcessStatus status) {
        updateFileStatus(fileId, status, null);
    }

    @Override
    @Transactional
    public void updateFileStatus(Long fileId, FileProcessStatus status, String errorMessage) {
        try {
            Optional<AiFiles> fileOptional = aiFilesRepository.findById(fileId);
            if (fileOptional.isPresent()) {
                AiFiles aiFiles = fileOptional.get();
                aiFiles.setAiStatus(status);
                if (StrUtil.isNotBlank(errorMessage)) {
                    aiFiles.setErrorMessage(errorMessage);
                }
                aiFilesRepository.save(aiFiles);
                log.debug("更新文件状态: fileId={}, status={}, error={}", fileId, status, errorMessage);
            }
        } catch (Exception e) {
            log.error("更新文件状态失败: fileId={}, status={}", fileId, status, e);
        }
    }

    @Override
    public FileProcessStatus getFileStatus(Long fileId) {
        return aiFilesRepository.findById(fileId)
                .map(AiFiles::getAiStatus)
                .orElse(null);
    }
}
