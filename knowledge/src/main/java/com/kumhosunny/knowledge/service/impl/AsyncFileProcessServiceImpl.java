package com.kumhosunny.knowledge.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.amazonaws.services.s3.model.S3Object;
import com.kumhosunny.common.entity.AiFiles;
import com.kumhosunny.common.entity.Document;
import com.kumhosunny.common.enums.FileProcessStatus;
import com.kumhosunny.common.enums.VectorizableFileType;
import com.kumhosunny.common.repository.AiFilesRepository;
import com.kumhosunny.common.util.HashUtils;
import com.kumhosunny.common.util.LiteratureQdrantUtils;
import com.kumhosunny.knowledge.service.AsyncFileProcessService;
import com.pig4cloud.plugin.oss.service.OssTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.List;
import java.util.Optional;

/**
 * 异步文件处理服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class AsyncFileProcessServiceImpl implements AsyncFileProcessService {

    @Autowired
    private AiFilesRepository aiFilesRepository;

    @Autowired
    private LiteratureQdrantUtils literatureQdrantService;

    @Autowired
    private OssTemplate ossTemplate;

    @Value("${app.document.parse-url:http://192.168.100.88:9998/parse}")
    private String documentParseUrl;

    @Value("${app.qdrant.collection-name:literature_collection}")
    private String collectionName;

    @Override
    @Async("taskExecutor")
    @Transactional
    public void processFileAsync(Long fileId, String type) {
        log.info("开始异步处理文件: fileId={}, type={}", fileId, type);

        try {
            Optional<AiFiles> fileOptional = aiFilesRepository.findById(fileId);
            if (!fileOptional.isPresent()) {
                log.error("文件不存在: fileId={}", fileId);
                return;
            }

            AiFiles aiFiles = fileOptional.get();

            // 检查文件类型是否支持向量化
            if (ObjectUtil.isEmpty(VectorizableFileType.fromExtension(aiFiles.getFileType()))) {
                log.info("文件类型不支持向量化: fileType={}", aiFiles.getFileType());
                updateFileStatus(fileId, FileProcessStatus.COMPLETED);
                return;
            }

            // 检查是否需要解析文档
            if (StrUtil.isBlank(type)) {
                log.info("未指定文档类型，跳过解析: fileId={}", fileId);
                updateFileStatus(fileId, FileProcessStatus.COMPLETED);
                return;
            }

            // 步骤1: 解析文件
            parseFile(aiFiles, type);

        } catch (Exception e) {
            log.error("异步处理文件失败: fileId={}", fileId, e);
            updateFileStatus(fileId, FileProcessStatus.FAILED, "处理失败: " + e.getMessage());
        }
    }

    /**
     * 解析文件内容
     */
    private void parseFile(AiFiles aiFiles, String type) {
        log.info("开始解析文件: fileId={}, fileName={}", aiFiles.getId(), aiFiles.getFileName());

        try {
            // 更新状态为解析中
            updateFileStatus(aiFiles.getId(), FileProcessStatus.PARSING);

            // 创建临时文件用于解析
            File tempFile = createTempFile(aiFiles);
            if (tempFile == null) {
                throw new RuntimeException("无法创建临时文件");
            }

            try {
                // 调用文档解析服务
                HttpRequest httpRequest = HttpUtil.createPost(documentParseUrl);
                httpRequest.charset("UTF-8");
                httpRequest.form("encoding", "UTF-8");
                httpRequest.form("files", tempFile);
                String parsedContent = httpRequest.execute().body();

                if (StrUtil.isBlank(parsedContent)) {
                    throw new RuntimeException("文档解析结果为空");
                }

                // 保存解析内容
                aiFiles.setTextContent(parsedContent);
                aiFilesRepository.save(aiFiles);

                // 更新状态为已解析
                updateFileStatus(aiFiles.getId(), FileProcessStatus.PARSED);

                log.info("文件解析完成: fileId={}, contentLength={}", aiFiles.getId(), parsedContent.length());

                // 步骤2: 向量化处理
                vectorizeFile(aiFiles, type, parsedContent);

            } finally {
                // 清理临时文件
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }

        } catch (Exception e) {
            log.error("文件解析失败: fileId={}", aiFiles.getId(), e);
            updateFileStatus(aiFiles.getId(), FileProcessStatus.FAILED, "解析失败: " + e.getMessage());
        }
    }

    /**
     * 向量化处理
     */
    private void vectorizeFile(AiFiles aiFiles, String type, String content) {
        log.info("开始向量化文件: fileId={}, type={}", aiFiles.getId(), type);

        try {
            // 更新状态为向量化中
            updateFileStatus(aiFiles.getId(), FileProcessStatus.VECTORIZING);

            // 内容去重检查
            if (shouldCheckDuplicates(type)) {
                String contentHash = HashUtils.normalizedContentHash(content);
                // TODO: 实现重复内容检查逻辑
                log.debug("内容哈希: {}", contentHash);
            }

            // 执行向量化
            Document.DocumentType documentType = Document.DocumentType.valueOf(type);
            int insertedChunks = literatureQdrantService.insertLiterature(content, collectionName, aiFiles,
                    documentType);

            log.info("文档向量化完成: fileId={}, 插入段落数: {}", aiFiles.getId(), insertedChunks);

            // 清理文本内容（节省存储空间）
            aiFiles.setTextContent("");
            aiFilesRepository.save(aiFiles);

            // 更新状态为完成
            updateFileStatus(aiFiles.getId(), FileProcessStatus.COMPLETED);

        } catch (Exception e) {
            log.error("文件向量化失败: fileId={}", aiFiles.getId(), e);
            updateFileStatus(aiFiles.getId(), FileProcessStatus.FAILED, "向量化失败: " + e.getMessage());
        }
    }

    /**
     * 创建临时文件
     */
    private File createTempFile(AiFiles aiFiles) {
        try {
            String tempFileName = aiFiles.getFileName();
            File tempFile = new File(System.getProperty("java.io.tmpdir") + "/" + tempFileName);

            // 从OSS下载文件到临时目录
            try (S3Object s3Object = ossTemplate.getObject(aiFiles.getBucketName(), aiFiles.getFileName())) {
                FileUtil.writeFromStream(s3Object.getObjectContent(), tempFile);
                log.debug("文件下载成功: {}", tempFile.getAbsolutePath());
                return tempFile;
            }

        } catch (Exception e) {
            log.error("创建临时文件失败: fileName={}", aiFiles.getFileName(), e);
            return null;
        }
    }

    /**
     * 判断是否需要检查重复内容
     */
    private boolean shouldCheckDuplicates(String type) {
        return "department".equals(type) || "company".equals(type);
    }

    @Override
    @Transactional
    public void updateFileStatus(Long fileId, FileProcessStatus status) {
        updateFileStatus(fileId, status, null);
    }

    @Override
    @Transactional
    public void updateFileStatus(Long fileId, FileProcessStatus status, String errorMessage) {
        try {
            Optional<AiFiles> fileOptional = aiFilesRepository.findById(fileId);
            if (fileOptional.isPresent()) {
                AiFiles aiFiles = fileOptional.get();
                aiFiles.setAiStatus(status);
                if (StrUtil.isNotBlank(errorMessage)) {
                    aiFiles.setErrorMessage(errorMessage);
                }
                aiFilesRepository.save(aiFiles);
                log.debug("更新文件状态: fileId={}, status={}, error={}", fileId, status, errorMessage);
            }
        } catch (Exception e) {
            log.error("更新文件状态失败: fileId={}, status={}", fileId, status, e);
        }
    }

    @Override
    public FileProcessStatus getFileStatus(Long fileId) {
        return aiFilesRepository.findById(fileId)
                .map(AiFiles::getAiStatus)
                .orElse(null);
    }
}
