package com.kumhosunny.app.controller;

import com.kumhosunny.common.dto.FileUploadResponse;
import com.kumhosunny.common.dto.FileUrlResponse;
import com.kumhosunny.common.result.ApiResponse;
import com.kumhosunny.common.util.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 文件上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/file")
public class FileUploadController {

    private final OssUtil ossUtil;

    // 允许上传的文件类型
    private static final List<String> ALLOWED_CONTENT_TYPES = Arrays.asList(
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp",
            "application/pdf", "text/plain", "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation");

    // 最大文件大小：10MB
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;

    public FileUploadController(OssUtil ossUtil) {
        this.ossUtil = ossUtil;
    }


    /**
     * 删除文件
     */
    @DeleteMapping("/delete")
    public ApiResponse<Boolean> deleteFile(@RequestParam("fileName") String fileName) {
        try {
            ossUtil.deleteFile(fileName);
            log.info("文件删除成功: {}", fileName);
            return ApiResponse.success(true);
        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage());
            return ApiResponse.error("文件删除失败: " + e.getMessage());
        }
    }

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException("文件大小不能超过10MB");
        }

        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_CONTENT_TYPES.contains(contentType)) {
            throw new IllegalArgumentException("不支持的文件类型: " + contentType);
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }
    }

    /**
     * 获取支持的文件类型
     */
    @GetMapping("/supported-types")
    public ApiResponse<List<String>> getSupportedTypes() {
        return ApiResponse.success(ALLOWED_CONTENT_TYPES);
    }

    /**
     * 获取OSS配置信息（用于调试）
     */
    @GetMapping("/config")
    public ApiResponse<String> getOssConfig() {
        return ApiResponse.success("OSS配置已加载");
    }

    /**
     * 获取文件外链URL
     * 注意：请使用上传接口返回的filePath参数作为fileName，而不是原始文件名
     */
    @GetMapping("/url")
    public ApiResponse<FileUrlResponse> getFileUrl(@RequestParam("fileName") String fileName) {
        try {
            // 检查文件是否存在
            if (!ossUtil.doesFileExist(fileName)) {
                return ApiResponse.error("文件不存在");
            }

            // 获取文件外链
            String fileUrl = ossUtil.getFileUrl(fileName);

            FileUrlResponse response = new FileUrlResponse(fileName, fileUrl);

            log.info("获取文件外链成功: {}", fileName);
            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("获取文件外链失败: {}", e.getMessage());
            return ApiResponse.error("获取文件外链失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件临时访问URL（带签名，用于私有文件访问）
     * 注意：请使用上传接口返回的filePath参数作为fileName，而不是原始文件名
     */
    @GetMapping("/presigned-url")
    public ApiResponse<FileUrlResponse> getPresignedUrl(
            @RequestParam("fileName") String fileName,
            @RequestParam(value = "expireSeconds", defaultValue = "3600") int expireSeconds) {
        try {
            // 检查文件是否存在
            if (!ossUtil.doesFileExist(fileName)) {
                return ApiResponse.error("文件不存在");
            }

            // 验证过期时间（最大7天）
            if (expireSeconds > 7 * 24 * 3600) {
                return ApiResponse.error("过期时间不能超过7天");
            }

            // 获取临时访问URL
            String presignedUrl = ossUtil.getPresignedUrl(fileName, expireSeconds);

            // 计算过期时间戳
            long expireTime = System.currentTimeMillis() + expireSeconds * 1000L;
            FileUrlResponse response = new FileUrlResponse(fileName, presignedUrl, expireTime);

            log.info("获取文件临时访问URL成功: {}", fileName);
            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("获取文件临时访问URL失败: {}", e.getMessage());
            return ApiResponse.error("获取文件临时访问URL失败: " + e.getMessage());
        }
    }
}