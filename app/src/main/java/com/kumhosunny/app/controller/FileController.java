package com.kumhosunny.app.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.kumhosunny.common.dto.FileDetailDto;
import com.kumhosunny.common.dto.ContextualSearchResult;
import com.kumhosunny.common.entity.EmployeeEntity;
import com.kumhosunny.common.repository.EmployeeRepository;
import com.kumhosunny.common.result.Result;
import com.kumhosunny.common.util.ElasticsearchUtil;
import com.kumhosunny.common.util.LiteratureQdrantUtils;
import com.kumhosunny.common.util.QdrantUtils;
import com.kumhosunny.common.util.UserContextUtil;
import com.kumhosunny.knowledge.req.AiFilesReq;
import com.kumhosunny.knowledge.service.FileService;
import io.qdrant.client.grpc.Points;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static io.qdrant.client.PointIdFactory.id;
import static io.qdrant.client.ValueFactory.value;
import static io.qdrant.client.VectorsFactory.vectors;

/**
 * @program: kum-ai-app-backend
 * @description:
 * @author: wye
 * @create: 2025-06-18 13:19
 **/
@RequestMapping("/api/ai/file")
@RestController
public class FileController {

    @Autowired
    private FileService fileService;

    @Autowired
    private UserContextUtil userContextUtil;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private LiteratureQdrantUtils literatureQdrantService;

    /**
     * 文件上传
     * 
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/upload")
    public Result upload(HttpServletRequest request,
            AiFilesReq req) throws Exception {
        return fileService.upload(request, req);
    }

    @GetMapping("/list")
    public Result<Page<FileDetailDto>> listFiles(
            HttpServletRequest request,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String originalFileName,
            Pageable pageable) {
        Long currentUserId = userContextUtil.getCurrentUserId(request);
        EmployeeEntity employee = employeeRepository.findById(currentUserId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        Long departmentId = employee.getDepartmentId();

        Page<FileDetailDto> fileDetails = fileService.findFileDetails(type, originalFileName, pageable, currentUserId,
                departmentId);
        return Result.success(fileDetails);
    }

    @GetMapping("/recall")
    public Result recall(@RequestParam Long fileId, @RequestParam String query) {
        return fileService.recall(fileId, query);
    }

    @GetMapping("/test")
    public String test(@RequestParam String query, @RequestParam Long userId, @RequestParam String type)
            throws ExecutionException, InterruptedException {

        try {
            // 使用新的上下文搜索功能
            List<ContextualSearchResult> contextualResults = literatureQdrantService.hybridSearchWithContext(
                    query, "literature_collection", 0.7, 10, userId, type, 2);

            if (CollUtil.isEmpty(contextualResults)) {
                return "知识库未搜索到结果";
            }

            StringBuilder builder = new StringBuilder();
            builder.append("📚 知识库搜索结果（含上下文）：")
                    .append("\n🔍 查询：").append(query)
                    .append("\n✅ 找到以下相关信息：");

            for (int i = 0; i < contextualResults.size(); i++) {
                ContextualSearchResult result = contextualResults.get(i);

                builder.append(String.format("\n\n📄 结果 %d：", i + 1))
                        .append(String.format("\n📋 文档：%s", result.getDocumentTitle()))
                        .append(String.format("\n⭐ 相似度：%.4f", result.getScore()))
                        .append(String.format("\n📍 匹配位置：第 %d 段", result.getMatchedChunk().getChunkIndex() + 1));

                // 显示完整的上下文内容
                String contextContent = result.getConcatenatedContent();
                // if (contextContent.length() > 500) {
                // // 如果内容太长，截取前500字符并添加省略号
                // contextContent = contextContent.substring(0, 500) + "...";
                // }
                builder.append(String.format("\n📝 上下文内容：%s", contextContent));

                // 显示上下文信息
                builder.append(String.format("\n🔗 上下文范围：包含前后共 %d 段内容", result.getContextChunks().size()));

                // 如果有多个段落，显示段落分布
                if (result.getContextChunks().size() > 1) {
                    List<Integer> chunkIndexes = result.getContextChunks().stream()
                            .map(chunk -> chunk.getChunkIndex() + 1)
                            .sorted()
                            .collect(Collectors.toList());
                    builder.append(String.format("\n📑 段落编号：%s", chunkIndexes.toString()));
                }
            }

            builder.append("\n\n⏱️ 搜索时间：").append(DateUtil.now());
            builder.append("\n💡 提示：搜索结果已包含上下文信息，避免断句问题");

            return builder.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 根据文档ID删除文档
     *
     * @param documentId 文档ID
     * @param request    HTTP请求对象
     * @return 删除结果
     */
    @DeleteMapping("/{documentId}")
    public Result deleteDocument(@PathVariable Long documentId, HttpServletRequest request) {
        return fileService.deleteDocument(documentId, request);
    }

}
