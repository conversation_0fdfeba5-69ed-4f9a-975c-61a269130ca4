package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.AiFiles;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import com.kumhosunny.common.dto.FileDetailDto;
import com.kumhosunny.common.entity.Document;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * AiFiles Repository接口
 *
 * <AUTHOR>
 */
@Repository
public interface AiFilesRepository extends JpaRepository<AiFiles, Long> {

    /**
     * 根据文件名查找文件
     */
    Optional<AiFiles> findByFileName(String fileName);

    /**
     * 根据原始文件名查找文件
     */
    List<AiFiles> findByOriginalFileName(String originalFileName);

    /**
     * 根据文件类型查找文件
     */
    List<AiFiles> findByFileType(String fileType);

    /**
     * 根据存储桶名称查找文件
     */
    List<AiFiles> findByBucketName(String bucketName);

    /**
     * 统计某个文件类型的文件数量
     */
    long countByFileType(String fileType);

    /**
     * 根据原始文件名模糊查询
     */
    List<AiFiles> findByOriginalFileNameContaining(String keyword);

    @Query(value = "SELECT new com.kumhosunny.common.dto.FileDetailDto(" +
            "af.id, d.ownerId, af.originalFileName, af.filePath, af.path, af.fileType, af.fileSize, CAST(af.aiStatus as string), af.uploadTime, "
            +
            "d.id, d.title, CAST(d.type as string), d.createdAt) " +
            "FROM AiFiles af JOIN Document d ON af.id = d.fileId " +
            "WHERE (:docType IS NULL OR d.type = :docType) " +
            "AND (:originalFileName IS NULL OR af.originalFileName LIKE CONCAT('%', :originalFileName, '%')) " +
            "AND (" +
            " (d.type = 'personal' AND d.ownerId = :currentUserId) OR " +
            " (d.type = 'department' AND d.departmentId IN :departmentIds) OR " +
            " (d.type = 'company') OR " +
            " (d.type = 'legal')" +
            ")", countQuery = "SELECT count(af.id) "
                    +
                    "FROM AiFiles af JOIN Document d ON af.id = d.fileId " +
                    "WHERE (:docType IS NULL OR d.type = :docType) " +
                    "AND (:originalFileName IS NULL OR af.originalFileName LIKE CONCAT('%', :originalFileName, '%'))" +
                    "AND (" +
                    " (d.type = 'personal' AND d.ownerId = :currentUserId) OR " +
                    " (d.type = 'department' AND d.departmentId IN :departmentIds) OR " +
                    " (d.type = 'company') OR " +
                    " (d.type = 'legal')" +
                    ")")
    Page<FileDetailDto> findFileDetails(@Param("docType") Document.DocumentType docType,
            @Param("originalFileName") String originalFileName,
            Pageable pageable,
            @Param("currentUserId") Long currentUserId,
            @Param("departmentIds") List<Long> departmentIds);

    /**
     * 查询文件详情（包含未关联Document的文件，排除图片附件，不区分权限）
     */
    @Query(value = "SELECT new com.kumhosunny.common.dto.FileDetailDto(" +
            "af.id, CAST(af.userId as long), af.originalFileName, af.filePath, af.path, af.fileType, af.fileSize, CAST(af.aiStatus as string), af.uploadTime, "
            +
            "d.id, d.title, CAST(d.type as string), d.createdAt) " +
            "FROM AiFiles af LEFT JOIN Document d ON af.id = d.fileId " +
            "WHERE af.userId = CAST(:currentUserId as string) " +
            "AND (:originalFileName IS NULL OR af.originalFileName LIKE CONCAT('%', :originalFileName, '%')) " +
            "AND (:docType IS NULL OR d.type IS NULL OR d.type = :docType) " +
            "AND af.fileType NOT IN ('jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg') " +
            "ORDER BY af.id DESC", countQuery = "SELECT count(af.id) " +
                    "FROM AiFiles af LEFT JOIN Document d ON af.id = d.fileId " +
                    "WHERE af.userId = CAST(:currentUserId as string) " +
                    "AND (:originalFileName IS NULL OR af.originalFileName LIKE CONCAT('%', :originalFileName, '%')) " +
                    "AND (:docType IS NULL OR d.type IS NULL OR d.type = :docType) " +
                    "AND af.fileType NOT IN ('jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg')")
    Page<FileDetailDto> findAllFileDetails(@Param("docType") Document.DocumentType docType,
            @Param("originalFileName") String originalFileName,
            Pageable pageable,
            @Param("currentUserId") Long currentUserId);

    /**
     * 查询用户有权限访问的所有非图片文件（包含权限控制）
     */
    @Query(value = "SELECT new com.kumhosunny.common.dto.FileDetailDto(" +
            "af.id, CAST(af.userId as long), af.originalFileName, af.filePath, af.path, af.fileType, af.fileSize, CAST(af.aiStatus as string), af.uploadTime, "
            +
            "d.id, d.title, CAST(d.type as string), d.createdAt) " +
            "FROM AiFiles af LEFT JOIN Document d ON af.id = d.fileId " +
            "WHERE (:originalFileName IS NULL OR af.originalFileName LIKE CONCAT('%', :originalFileName, '%')) " +
            "AND (:docType IS NULL OR d.type IS NULL OR d.type = :docType) " +
            "AND af.fileType NOT IN ('jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg') " +
            "AND (" +
            "  af.userId = CAST(:currentUserId as string) " + // 用户上传的文件
            "  OR (d.type = 'personal' AND d.ownerId = :currentUserId) " + // 用户拥有的个人文档
            "  OR (d.type = 'department' AND d.departmentId IN :departmentIds) " + // 部门文档
            "  OR (d.type = 'company') " + // 公司文档
            "  OR (d.type = 'legal') " + // 法务文档
            "  OR d.id IS NULL " + // 未关联文档的文件（只能是用户自己上传的）
            ") " +
            "ORDER BY af.id DESC", countQuery = "SELECT count(af.id) " +
                    "FROM AiFiles af LEFT JOIN Document d ON af.id = d.fileId " +
                    "WHERE (:originalFileName IS NULL OR af.originalFileName LIKE CONCAT('%', :originalFileName, '%')) "
                    +
                    "AND (:docType IS NULL OR d.type IS NULL OR d.type = :docType) " +
                    "AND af.fileType NOT IN ('jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg') " +
                    "AND (" +
                    "  af.userId = CAST(:currentUserId as string) " +
                    "  OR (d.type = 'personal' AND d.ownerId = :currentUserId) " +
                    "  OR (d.type = 'department' AND d.departmentId IN :departmentIds) " +
                    "  OR (d.type = 'company') " +
                    "  OR (d.type = 'legal') " +
                    "  OR d.id IS NULL " +
                    ")")
    Page<FileDetailDto> findUserNonImageFiles(@Param("docType") Document.DocumentType docType,
            @Param("originalFileName") String originalFileName,
            Pageable pageable,
            @Param("currentUserId") Long currentUserId,
            @Param("departmentIds") List<Long> departmentIds);
}